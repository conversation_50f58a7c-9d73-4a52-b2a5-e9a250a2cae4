import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class CampusesRepositoryLive extends Effect.Service<CampusesRepositoryLive>()(
  'CampusesRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      const findAllCampuses = db.makeQuery((exec) =>
        exec((client) =>
          client.query.campuses.findMany({
            columns: {
              id: true,
              sadId: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: { id: true, locale: true, name: true },
              },
            },
          }),
        ),
      );

      const findCampusById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.campuses.findFirst({
            where: eq(DBSchema.campuses.id, id),
            columns: {
              id: true,
              sadId: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: { id: true, locale: true, name: true },
              },
            },
          }),
        ),
      );

      const createCampus = (params: {
        campus: {
          sadId?: string | null;
          institutionId: string;
        };
        translations: Array<{
          locale: string;
          name?: string | null;
        }>;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            const [campus] = yield* tx((client) =>
              client
                .insert(DBSchema.campuses)
                .values({
                  sadId: params.campus.sadId,
                  institutionId: params.campus.institutionId,
                })
                .returning({ id: DBSchema.campuses.id }),
            );

            yield* tx((client) =>
              client.insert(DBSchema.campusesI18N).values(
                params.translations.map((t) => ({
                  dataId: campus.id,
                  locale: t.locale,
                  name: t.name,
                })),
              ),
            );

            // Fetch and return the complete campus with translations
            const campusWithTranslations = yield* tx((client) =>
              client.query.campuses.findFirst({
                where: eq(DBSchema.campuses.id, campus.id),
                columns: {
                  id: true,
                  sadId: true,
                  institutionId: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: { id: true, locale: true, name: true },
                  },
                },
              }),
            );

            return campusWithTranslations;
          });
        });
      };

      const updateCampus = (params: {
        id: string;
        sadId?: string | null;
        institutionId?: string;
        modifiedBy: string;
        translations?: Array<{
          locale: string;
          name?: string | null;
        }>;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the campus
            yield* tx((client) =>
              client
                .update(DBSchema.campuses)
                .set({
                  sadId: params.sadId,
                  institutionId: params.institutionId,
                  modifiedBy: params.modifiedBy,
                })
                .where(eq(DBSchema.campuses.id, params.id))
                .returning({ id: DBSchema.campuses.id }),
            );

            // Update translations if provided
            if (params.translations) {
              const translations = params.translations;
              // Delete existing translations
              yield* tx((client) =>
                client
                  .delete(DBSchema.campusesI18N)
                  .where(eq(DBSchema.campusesI18N.dataId, params.id)),
              );

              // Insert new translations
              yield* tx((client) =>
                client.insert(DBSchema.campusesI18N).values(
                  translations.map((t) => ({
                    dataId: params.id,
                    locale: t.locale,
                    name: t.name,
                  })),
                ),
              );
            }

            // Fetch and return the complete campus with translations
            const campusWithTranslations = yield* tx((client) =>
              client.query.campuses.findFirst({
                where: eq(DBSchema.campuses.id, params.id),
                columns: {
                  id: true,
                  sadId: true,
                  institutionId: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: { id: true, locale: true, name: true },
                  },
                },
              }),
            );

            return campusWithTranslations;
          });
        });
      };

      const deleteCampus = (id: string) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // First delete all translations for this campus
            yield* tx((client) =>
              client
                .delete(DBSchema.campusesI18N)
                .where(eq(DBSchema.campusesI18N.dataId, id)),
            );

            // Then delete the campus itself
            const result = yield* tx((client) =>
              client
                .delete(DBSchema.campuses)
                .where(eq(DBSchema.campuses.id, id))
                .returning({ id: DBSchema.campuses.id }),
            );

            return result;
          });
        });
      };

      return {
        findAllCampuses,
        findCampusById,
        createCampus,
        updateCampus,
        deleteCampus,
      } as const;
    }),
  },
) { }
