import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import type { I18nRow } from '@rie/domain/types';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class VendorsRepositoryLive extends Effect.Service<VendorsRepositoryLive>()(
  'VendorsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findAllVendors = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.vendors.findMany({
            columns: {
              id: true,
              startDate: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const findVendorById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.vendors.findFirst({
            where: eq(DBSchema.vendors.id, id),
            columns: {
              id: true,
              startDate: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const createVendor = (params: {
        vendor: {
          startDate: string;
          endDate: string;
        };
        translations: I18nRow[];
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // First create the vendor
            const [vendor] = yield* tx((client) =>
              client
                .insert(DBSchema.vendors)
                .values({
                  startDate: params.vendor.startDate,
                  endDate: params.vendor.endDate,
                })
                .returning({
                  id: DBSchema.vendors.id,
                  startDate: DBSchema.vendors.startDate,
                  endDate: DBSchema.vendors.endDate,
                  createdAt: DBSchema.vendors.createdAt,
                  updatedAt: DBSchema.vendors.updatedAt,
                  modifiedBy: DBSchema.vendors.modifiedBy,
                }),
            );

            // Then create the translations if provided
            const translationsToInsert = params.translations.map(
              (translation) => ({
                dataId: vendor.id,
                locale: translation.locale,
                name: translation.name,
                website: translation.website,
                description: translation.description,
                otherNames: translation.otherNames,
              }),
            );

            yield* tx((client) =>
              client
                .insert(DBSchema.vendorsI18N)
                .values(translationsToInsert),
            );

            // Return vendor with translations
            return yield* tx((client) =>
              client.query.vendors.findFirst({
                where: eq(DBSchema.vendors.id, vendor.id),
                columns: {
                  id: true,
                  startDate: true,
                  endDate: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      website: true,
                      description: true,
                      otherNames: true,
                    },
                  },
                },
              }),
            );
          });
        });
      };

      const updateVendor = (data: {
        id: string;
        startDate?: string | null;
        endDate?: string | null;
        modifiedBy: string;
        translations?: I18nRow[];
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // First update the vendor
            yield* tx((client) =>
              client
                .update(DBSchema.vendors)
                .set({
                  startDate: data.startDate,
                  endDate: data.endDate,
                  modifiedBy: data.modifiedBy,
                })
                .where(eq(DBSchema.vendors.id, data.id)),
            );

            // Update translations if provided
            if (data.translations && data.translations.length > 0) {
              // Delete existing translations
              yield* tx((client) =>
                client
                  .delete(DBSchema.vendorsI18N)
                  .where(eq(DBSchema.vendorsI18N.dataId, data.id)),
              );

              // Insert new translations
              const translationsToInsert = data.translations.map(
                (translation) => ({
                  dataId: data.id,
                  locale: translation.locale,
                  name: translation.name,
                  website: translation.website,
                  description: translation.description,
                  otherNames: translation.otherNames,
                }),
              );

              yield* tx((client) =>
                client
                  .insert(DBSchema.vendorsI18N)
                  .values(translationsToInsert),
              );
            }

            // Return vendor with translations
            return yield* tx((client) =>
              client.query.vendors.findFirst({
                where: eq(DBSchema.vendors.id, data.id),
                columns: {
                  id: true,
                  startDate: true,
                  endDate: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      website: true,
                      description: true,
                      otherNames: true,
                    },
                  },
                },
              }),
            );
          });
        });
      };

      const deleteVendor = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.vendors)
            .where(eq(DBSchema.vendors.id, id))
            .returning({ id: DBSchema.vendors.id }),
        );
      });

      return {
        findAllVendors,
        findVendorById,
        createVendor,
        updateVendor,
        deleteVendor,
      } as const;
    }),
  },
) { }
