import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class FundingProjectsRepositoryLive extends Effect.Service<FundingProjectsRepositoryLive>()(
  'FundingProjectsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      // — Fetch all projects
      const findAllFundingProjects = db.makeQuery((exec) =>
        exec((client) =>
          client.query.fundingProjects.findMany({
            columns: {
              id: true,
              holderId: true,
              typeId: true,
              fciId: true,
              synchroId: true,
              obtainingYear: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                },
              },
            },
          }),
        ),
      );

      // — Fetch one
      const findFundingProjectById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.fundingProjects.findFirst({
            where: eq(DBSchema.fundingProjects.id, id),
            columns: {
              id: true,
              holderId: true,
              typeId: true,
              fciId: true,
              synchroId: true,
              obtainingYear: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                },
              },
            },
          }),
        ),
      );

      const createFundingProject = (params: {
        project: {
          holderId: string;
          typeId: string;
          fciId?: string | null;
          synchroId: string;
          obtainingYear: number;
          endDate?: string | null;
        };
        translations: Array<{
          locale: string;
          name: string;
          description?: string | null;
        }>;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            const [proj] = yield* tx((client) =>
              client
                .insert(DBSchema.fundingProjects)
                .values({
                  holderId: params.project.holderId,
                  typeId: params.project.typeId,
                  fciId: params.project.fciId,
                  synchroId: params.project.synchroId,
                  obtainingYear: params.project.obtainingYear,
                  endDate: params.project.endDate,
                })
                .returning({ id: DBSchema.fundingProjects.id }),
            );

            yield* tx((client) =>
              client.insert(DBSchema.fundingProjectsI18N).values(
                params.translations.map((t) => ({
                  dataId: proj.id,
                  locale: t.locale,
                  name: t.name,
                  description: t.description,
                })),
              ),
            );

            // Fetch and return the complete funding project with translations
            const projectWithTranslations = yield* tx((client) =>
              client.query.fundingProjects.findFirst({
                where: eq(DBSchema.fundingProjects.id, proj.id),
                columns: {
                  id: true,
                  holderId: true,
                  typeId: true,
                  fciId: true,
                  synchroId: true,
                  obtainingYear: true,
                  endDate: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              }),
            );

            return projectWithTranslations;
          });
        });
      };

      const updateFundingProject = (params: {
        id: string;
        project: {
          holderId?: string;
          typeId?: string;
          fciId?: string | null;
          synchroId?: string;
          obtainingYear?: number;
          endDate?: string | null;
          modifiedBy: string;
        };
        translations: Array<{
          locale: string;
          name: string;
          description?: string | null;
        }>;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the project
            yield* tx((client) =>
              client
                .update(DBSchema.fundingProjects)
                .set({
                  holderId: params.project.holderId,
                  typeId: params.project.typeId,
                  fciId: params.project.fciId,
                  synchroId: params.project.synchroId,
                  obtainingYear: params.project.obtainingYear,
                  endDate: params.project.endDate,
                  modifiedBy: params.project.modifiedBy,
                })
                .where(eq(DBSchema.fundingProjects.id, params.id))
                .returning({
                  id: DBSchema.fundingProjects.id,
                  holderId: DBSchema.fundingProjects.holderId,
                  typeId: DBSchema.fundingProjects.typeId,
                  fciId: DBSchema.fundingProjects.fciId,
                  synchroId: DBSchema.fundingProjects.synchroId,
                  obtainingYear: DBSchema.fundingProjects.obtainingYear,
                  endDate: DBSchema.fundingProjects.endDate,
                  createdAt: DBSchema.fundingProjects.createdAt,
                  updatedAt: DBSchema.fundingProjects.updatedAt,
                  modifiedBy: DBSchema.fundingProjects.modifiedBy,
                }),
            );

            // Update translations - delete old and insert new
            yield* tx((client) =>
              client
                .delete(DBSchema.fundingProjectsI18N)
                .where(eq(DBSchema.fundingProjectsI18N.dataId, params.id)),
            );
            yield* tx((client) =>
              client.insert(DBSchema.fundingProjectsI18N).values(
                params.translations.map((t) => ({
                  dataId: params.id,
                  locale: t.locale,
                  name: t.name,
                  description: t.description,
                })),
              ),
            );


            // Fetch and return the complete funding project with translations
            const projectWithTranslations = yield* tx((client) =>
              client.query.fundingProjects.findFirst({
                where: eq(DBSchema.fundingProjects.id, params.id),
                columns: {
                  id: true,
                  holderId: true,
                  typeId: true,
                  fciId: true,
                  synchroId: true,
                  obtainingYear: true,
                  endDate: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              }),
            );

            return projectWithTranslations;
          });
        });
      };

      // — Delete
      const deleteFundingProject = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.fundingProjects)
            .where(eq(DBSchema.fundingProjects.id, id))
            .returning({ id: DBSchema.fundingProjects.id }),
        ),
      );

      return {
        findAllFundingProjects,
        findFundingProjectById,
        createFundingProject,
        updateFundingProject,
        deleteFundingProject,
      } as const;
    }),
  },
) { }
