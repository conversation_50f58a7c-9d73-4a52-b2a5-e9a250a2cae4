import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { infrastructures } from '@rie/db-schema/schemas';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class InfrastructuresRepositoryLive extends Effect.Service<InfrastructuresRepositoryLive>()(
  'InfrastructuresRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findInfrastructuresByUnitId = dbClient.makeQuery((execute, unitId: string) => {
        return execute((client) =>
          client.query.infrastructures.findMany({
            where: eq(infrastructures.unitId, unitId),
            columns: {
              id: true,
            },
          }),
        );
      });


      const findInfrastructureById = dbClient.makeQuery(
        (execute, infrastructureId: string) => {
          return execute((client) =>
            client.query.infrastructures.findFirst({
              where: eq(infrastructures.id, infrastructureId),
              columns: {
                id: true,
                guidId: true,
                typeId: true,
                addressId: true,
                statusId: true,
                unitId: true,
                website: true,
                is_featured: true,
                visibilityId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                    description: true,
                    otherNames: true,
                    acronyms: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                status: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                visibility: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                unit: {
                  columns: {
                    id: true,
                    guidId: true,
                    typeId: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      /**
       * Find all infrastructures (for testing/debugging purposes)
       */
      const findAllInfrastructures = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.infrastructures.findMany({
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              addressId: true,
              statusId: true,
              unitId: true,
              website: true,
              is_featured: true,
              visibilityId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
            },
          }),
        );
      });

      const createInfrastructure = (params: {
        infrastructure: {
          guidId: string;
          typeId: string;
          addressId?: string | null;
          statusId: string;
          website?: string | null;
          is_featured?: boolean;
          visibilityId: string;
          unitId: string;
        };
        translations: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          otherNames?: string | null;
          acronyms?: string | null;
        }>;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            const [inf] = yield* tx((client) =>
              client
                .insert(DBSchema.infrastructures)
                .values({
                  guidId: params.infrastructure.guidId,
                  typeId: params.infrastructure.typeId,
                  addressId: params.infrastructure.addressId,
                  statusId: params.infrastructure.statusId,
                  unitId: params.infrastructure.unitId,
                  website: params.infrastructure.website,
                  is_featured: params.infrastructure.is_featured,
                  visibilityId: params.infrastructure.visibilityId,
                })
                .returning({ id: DBSchema.infrastructures.id }),
            );

            const rows = params.translations.map((t) => ({
              dataId: inf.id,
              locale: t.locale,
              name: t.name,
              description: t.description,
              otherNames: t.otherNames,
              acronyms: t.acronyms,
            }));
            yield* tx((client) =>
              client.insert(DBSchema.infrastructuresI18N).values(rows),
            );

            return inf;
          });
        });
      };

      const updateInfrastructure = (params: {
        id: string;
        infrastructure: {
          guidId?: string;
          typeId?: string;
          addressId?: string | null;
          statusId?: string;
          website?: string | null;
          is_featured?: boolean;
          visibilityId?: string;
          modifiedBy: string;
          unitId: string;
        };
        translations: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          otherNames?: string | null;
          acronyms?: string | null;
        }>;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the infrastructure
            const [infrastructure] = yield* tx((client) =>
              client
                .update(DBSchema.infrastructures)
                .set({
                  guidId: params.infrastructure.guidId,
                  typeId: params.infrastructure.typeId,
                  addressId: params.infrastructure.addressId,
                  statusId: params.infrastructure.statusId,
                  website: params.infrastructure.website,
                  is_featured: params.infrastructure.is_featured,
                  visibilityId: params.infrastructure.visibilityId,
                  modifiedBy: params.infrastructure.modifiedBy,
                  unitId: params.infrastructure.unitId,
                })
                .where(eq(DBSchema.infrastructures.id, params.id))
                .returning({
                  id: DBSchema.infrastructures.id,
                  guidId: DBSchema.infrastructures.guidId,
                  typeId: DBSchema.infrastructures.typeId,
                  addressId: DBSchema.infrastructures.addressId,
                  statusId: DBSchema.infrastructures.statusId,
                  website: DBSchema.infrastructures.website,
                  is_featured: DBSchema.infrastructures.is_featured,
                  visibilityId: DBSchema.infrastructures.visibilityId,
                  createdAt: DBSchema.infrastructures.createdAt,
                  updatedAt: DBSchema.infrastructures.updatedAt,
                  modifiedBy: DBSchema.infrastructures.modifiedBy,
                }),
            );

            // Update translations - delete old and insert new
            yield* tx((client) =>
              client
                .delete(DBSchema.infrastructuresI18N)
                .where(eq(DBSchema.infrastructuresI18N.dataId, params.id)),
            );

            const rows = params.translations.map((t) => ({
              dataId: params.id,
              locale: t.locale,
              name: t.name,
              description: t.description,
              otherNames: t.otherNames,
              acronyms: t.acronyms,
            }));
            yield* tx((client) =>
              client.insert(DBSchema.infrastructuresI18N).values(rows),
            );

            return infrastructure;
          });
        });
      };

      const deleteInfrastructure = dbClient.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.infrastructures)
            .where(eq(DBSchema.infrastructures.id, id))
            .returning({ id: DBSchema.infrastructures.id }),
        ),
      );

      return {
        findInfrastructuresByUnitId,
        findInfrastructureById,
        findAllInfrastructures,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
      } as const;
    }),
  },
) { }
