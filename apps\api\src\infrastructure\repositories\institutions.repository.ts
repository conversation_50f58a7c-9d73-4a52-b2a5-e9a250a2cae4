import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class InstitutionsRepositoryLive extends Effect.Service<InstitutionsRepositoryLive>()(
  'InstitutionsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      // — Fetch all
      const findAllInstitutions = db.makeQuery((exec) =>
        exec((client) =>
          client.query.institutions.findMany({
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  acronyms: true,
                  otherNames: true,
                },
              },
            },
          }),
        ),
      );

      // — Fetch one by ID
      const findInstitutionById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.institutions.findFirst({
            where: eq(DBSchema.institutions.id, id),
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  acronyms: true,
                  otherNames: true,
                },
              },
            },
          }),
        ),
      );

      const createInstitution = (params: {
        institution: {
          guidId: string;
          typeId: string;
        };
        translations: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          acronyms?: string | null;
          otherNames?: string | null;
        }>;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // 1) Insert main institution row
            const [inst] = yield* tx((client) =>
              client
                .insert(DBSchema.institutions)
                .values({
                  guidId: params.institution.guidId,
                  typeId: params.institution.typeId,
                })
                .returning({
                  id: DBSchema.institutions.id,
                  guidId: DBSchema.institutions.guidId,
                  typeId: DBSchema.institutions.typeId,
                  createdAt: DBSchema.institutions.createdAt,
                  updatedAt: DBSchema.institutions.updatedAt,
                  modifiedBy: DBSchema.institutions.modifiedBy,
                }),
            );

            // 2) Insert translations
            const rows = params.translations.map((t) => ({
              dataId: inst.id,
              locale: t.locale,
              name: t.name,
              description: t.description,
              acronyms: t.acronyms,
              otherNames: t.otherNames,
            }));
            yield* tx((client) =>
              client.insert(DBSchema.institutionsI18N).values(rows),
            );

            return inst;
          });
        });
      };

      const updateInstitution = (params: {
        id: string;
        guidId?: string;
        typeId?: string;
        modifiedBy: string;
        translations?: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          acronyms?: string | null;
          otherNames?: string | null;
        }>;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // 1) Update the institution
            const [inst] = yield* tx((client) =>
              client
                .update(DBSchema.institutions)
                .set({
                  guidId: params.guidId,
                  typeId: params.typeId,
                  modifiedBy: params.modifiedBy,
                })
                .where(eq(DBSchema.institutions.id, params.id))
                .returning({
                  id: DBSchema.institutions.id,
                  guidId: DBSchema.institutions.guidId,
                  typeId: DBSchema.institutions.typeId,
                  createdAt: DBSchema.institutions.createdAt,
                  updatedAt: DBSchema.institutions.updatedAt,
                  modifiedBy: DBSchema.institutions.modifiedBy,
                }),
            );

            // 2) Update translations if provided
            if (params.translations) {
              const translations = params.translations;
              // Delete existing translations
              yield* tx((client) =>
                client
                  .delete(DBSchema.institutionsI18N)
                  .where(eq(DBSchema.institutionsI18N.dataId, params.id)),
              );

              // Insert new translations
              yield* tx((client) =>
                client.insert(DBSchema.institutionsI18N).values(
                  translations.map((t) => ({
                    dataId: params.id,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                    acronyms: t.acronyms,
                    otherNames: t.otherNames,
                  })),
                ),
              );
            }

            return inst;
          });
        });
      };

      // — Delete institution
      const deleteInstitution = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.institutions)
            .where(eq(DBSchema.institutions.id, id))
            .returning({ id: DBSchema.institutions.id }),
        ),
      );

      return {
        findAllInstitutions,
        findInstitutionById,
        createInstitution,
        updateInstitution,
        deleteInstitution,
      } as const;
    }),
  },
) { }
